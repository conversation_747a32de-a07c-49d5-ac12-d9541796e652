# 批量导入项目数据说明

## 📋 功能概述
这个脚本用于从Excel文件批量导入高压用户项目数据到数据库中。

## 🚀 使用方法

### 1. 创建示例Excel文件
```bash
python batch_import_projects.py --create-sample
```
这将创建一个名为 `高压用户_示例.xlsx` 的示例文件，展示正确的数据格式。

### 2. 准备数据文件
- 将您的数据文件命名为 `高压用户.xlsx`
- 放置在脚本同一目录下
- 确保Excel文件包含以下列（按顺序）：

| 列名 | 说明 | 是否必填 | 示例 |
|------|------|----------|------|
| bao_city | 城市 | ✅ | 城区 |
| gds | 供电所 | ✅ | 城区专线 |
| gdlx | 工单类型 | ❌ | 新装/增容/临时用电 |
| bao_bm | 工单编号 | ✅ | 20241201001 |
| bao_name | 项目名称 | ✅ | 示例项目1 |
| usercode | 用户户号 | ✅ | 1001 |
| sj_ywsl | 业务受理时间 | ❌ | 2024-01-15 09:00:00 |
| sj_xckc | 现场勘察时间 | ❌ | 2024-01-20 14:00:00 |
| sj_fadf | 方案答复时间 | ❌ | 2024-01-25 16:00:00 |
| sj_jgys | 竣工验收时间 | ❌ | 2024-02-01 15:00:00 |
| sj_zbsd | 装表送电时间 | ❌ | 2024-02-05 10:00:00 |
| sj_gdsj | 项目归档时间 | ❌ | 2024-02-10 16:00:00 |
| htrl | 合同容量 | ❌ | 100.5 |

### 3. 执行导入
```bash
python batch_import_projects.py
```

## 📝 数据格式要求

### 必填字段验证
- **bao_name**: 项目名称，不能为空
- **bao_bm**: 工单编号，必须是纯数字，不能重复
- **bao_city**: 城市，必须在允许列表中（城区、环城、汝南、平舆、新蔡、确山、泌阳、正阳、遂平、西平、上蔡）
- **usercode**: 用户户号，不能为空
- **gds**: 供电所，不能为空

### 可选字段格式
- **gdlx**: 工单类型，如果填写必须是：增容、临时用电、新装
- **时间字段**: 支持多种格式
  - `2024-01-15 09:00:00`
  - `2024-01-15`
  - `2024/01/15 09:00:00`
  - Excel日期格式
- **htrl**: 合同容量，数值类型，支持小数

## 🔧 脚本功能特性

### 数据验证
- ✅ 必填字段检查
- ✅ 城市有效性验证
- ✅ 工单编号格式验证
- ✅ 工单编号重复性检查
- ✅ 工单类型有效性验证
- ✅ 时间格式自动转换
- ✅ 数值字段格式验证

### 导入流程
1. 📁 读取Excel文件
2. 🔍 数据预览（前5行）
3. ❓ 用户确认
4. 🔄 批量导入
5. 📊 结果统计
6. 📝 错误日志

### 安全特性
- 🔒 事务处理，失败自动回滚
- 📝 详细日志记录
- 🚫 重复数据检查
- ⚡ 分批提交，避免内存溢出

## 📊 输出信息

### 成功导入
```
📊 导入结果统计:
✅ 成功导入: 150 条
⚠️  跳过记录: 5 条
❌ 错误记录: 2 条
📝 总处理: 157 条
```

### 错误日志
- 控制台显示前20个错误
- 完整日志保存到 `batch_import.log`
- 包含具体行号和错误原因

## ⚠️ 注意事项

1. **数据库备份**: 导入前请备份数据库
2. **文件格式**: 确保Excel文件格式正确
3. **权限检查**: 确保有数据库写入权限
4. **网络连接**: 确保数据库连接正常
5. **文件路径**: Excel文件必须在脚本同一目录

## 🐛 常见问题

### Q: 提示"文件不存在"
A: 确保 `高压用户.xlsx` 文件在脚本同一目录下

### Q: 提示"数据库连接失败"
A: 检查 `app.py` 中的数据库配置是否正确

### Q: 某些行被跳过
A: 检查错误日志，通常是必填字段缺失或格式不正确

### Q: 时间字段导入失败
A: 确保时间格式正确，支持的格式见上述说明

## 📞 技术支持
如有问题，请检查 `batch_import.log` 日志文件获取详细错误信息。
